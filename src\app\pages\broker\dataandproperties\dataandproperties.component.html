<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-3">
      <div class="flex-grow-1">
        <div
          class="d-flex justify-content-between align-items-start flex-wrap mb-2"
        >
          <div class="card-header border-0 pt-5">
            <h3 class="card-title align-items-start flex-column">
              <span class="text-dark-blue fs-2 fw-bolder me-1"
                >Data and Properties</span
              >
              <span class="text-muted mt-1 fw-bold fs-7"
                >View my data and my properties</span
              >
            </h3>
          </div>

          <div class="d-flex my-4">
            <form
              data-kt-search-element="form"
              class="w-300px position-relative mb-3"
              autocomplete="off"
            >
              <app-keenicon
                name="magnifier"
                class="fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3"
                type="outline"
              >
              </app-keenicon>
              <input
                type="text"
                name="searchText"
                class="form-control form-control-flush ps-10 bg-light border rounded-pill"
                [(ngModel)]="searchText"
                (ngModelChange)="onSearchTextChange($event)"
                placeholder="Search By Unit Type.."
                data-kt-search-element="input"
              />
            </form>
          </div>

          <div
            class="d-flex flex-wrap align-items-start my-4 position-relative"
          >
            <!-- Filter Button -->
            <a
              class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer"
              (click)="toggleFilterDropdown()"
            >
              <i class="fa-solid fa-filter"></i> Filter
            </a>

            <!-- Filter Dropdown -->
            <div
              *ngIf="isFilterDropdownVisible"
              class="dropdown-menu show p-3 shadow"
              style="position: absolute; top: 100%; left: 0; z-index: 1000"
            >
              <app-unit-filter
                (filtersApplied)="onFiltersApplied($event)"
              ></app-unit-filter>
            </div>

            <!-- Upload Units Button -->
            <input
              type="file"
              #fileInput
              (change)="onFileSelected($event)"
              accept=".xlsx,.xls"
              hidden
            />
            <a
              class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer"
              (click)="fileInput.click()"
            >
              <i class="fa-solid fa-upload"></i> Upload Units
            </a>

            <!-- Download Template Button -->
            <a
              class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer"
              (click)="downloadTemplate()"
            >
              <i class="fa-solid fa-download"></i> Download Template
            </a>

            <!-- Add New Property -->
            <a
              [routerLink]="['/broker/add-property']"
              class="btn btn-sm btn-dark-blue me-3 cursor-pointer"
            >
              <i class="fa-solid fa-plus"></i> Add New Property
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div
      *ngIf="isLoading"
      class="d-flex justify-content-center align-items-center py-10"
    >
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <!-- Properties Table -->
    <app-propertiestable
      *ngIf="
        !isLoading && !showEmptyCard && !showSuccessCard && !showPublishCard
      "
      [appliedFilters]="appliedFilters"
    >
    </app-propertiestable>

    <!-- Empty Properties Card -->
    <app-empty-properties-card
      *ngIf="!isLoading && showEmptyCard"
      [userRole]="'broker'"
    >
    </app-empty-properties-card>

    <!-- Publish Property Card -->
    <app-publish-property-card
      *ngIf="!isLoading && showPublishCard"
      (backToTable)="onBackToTable()"
    >
    </app-publish-property-card>

    <!-- Success Adding Property Card -->
    <app-success-adding-property-card
      *ngIf="!isLoading && showSuccessCard"
      (backToTable)="onBackToTable()"
    >
    </app-success-adding-property-card>
  </div>
</div>
