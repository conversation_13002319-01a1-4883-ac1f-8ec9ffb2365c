{"name": "demo1", "version": "8.2.7", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "rtl": "webpack --config=rtl.config.js", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^18.1.4", "@angular/common": "^18.1.4", "@angular/compiler": "^18.1.4", "@angular/core": "^18.1.4", "@angular/forms": "^18.1.4", "@angular/localize": "^18.1.4", "@angular/material": "^18.1.4", "@angular/platform-browser": "^18.1.4", "@angular/platform-browser-dynamic": "^18.1.4", "@angular/router": "^18.1.4", "@fortawesome/fontawesome-free": "^6.3.0", "@ng-bootstrap/ng-bootstrap": "^17.0.0", "@ngx-translate/core": "15.0.0", "@ngx-translate/http-loader": "8.0.0", "@popperjs/core": "2.11.8", "@sweetalert2/ngx-sweetalert2": "^12.2.0", "angular-datatables": "^18.0.0", "angular-in-memory-web-api": "^0.18.0", "animate.css": "4.1.1", "apexcharts": "^3.54.1", "bootstrap": "5.3.2", "bootstrap-icons": "^1.10.3", "chart.js": "^4.4.9", "clipboard": "2.0.11", "datatables.net": "^2.0.4", "datatables.net-bs5": "^2.0.4", "file-saver": "^2.0.5", "jquery": "^3.6.0", "line-awesome": "1.3.0", "moment": "^2.29.4", "ng-apexcharts": "^1.15.0", "ng-inline-svg-2": "^15.0.1", "ng2-charts": "^8.0.0", "ngx-clipboard": "16.0.0", "nouislider": "^15.7.0", "object-path": "0.11.8", "prism-themes": "1.9.0", "prismjs": "^1.29.0", "rxjs": "~7.8.0", "socicon": "3.0.5", "sweetalert2": "11.4.8", "tslib": "^2.3.0", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^18.1.4", "@angular-eslint/builder": "^18.1.0", "@angular-eslint/eslint-plugin": "^18.1.0", "@angular-eslint/eslint-plugin-template": "^18.1.0", "@angular-eslint/schematics": "^18.1.0", "@angular-eslint/template-parser": "^18.1.0", "@angular/cli": "^18.1.4", "@angular/compiler-cli": "^18.1.4", "@types/bootstrap": "^5.2.6", "@types/datatables.net": "^1.12.0", "@types/file-saver": "^2.0.7", "@types/jasmine": "~5.1.4", "@types/jquery": "^3.5.29", "@types/node": "^22.2.0", "@types/object-path": "0.11.1", "@types/prismjs": "1.26.0", "@types/sass-loader": "8.0.3", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "css-loader": "^7.1.1", "del": "^7.0.0", "eslint": "^9.8.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "mini-css-extract-plugin": "^2.9.0", "rtlcss-webpack-plugin": "4.0.7", "sass-loader": "^16.0.0", "typescript": "~5.5.4", "webpack": "^5.93.0", "webpack-cli": "5.1.4"}}