import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UnitService } from '../services/unit.service';

@Component({
  selector: 'app-dataandproperties',
  templateUrl: './dataandproperties.component.html',
  styleUrl: './dataandproperties.component.scss',
})
export class DataandpropertiesComponent implements OnInit {
  showEmptyCard = false;
  showSuccessCard = false;
  showPublishCard = false;
  isFilterDropdownVisible = false;

  brokerId = 5;

  // Data properties
  properties: any[] = [];
  isLoading = true;
  appliedFilters: any = {};
  searchText: string = '';
  private searchTimeout: any;

  constructor(
    private unitService: UnitService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.checkRouteParams();
    this.loadPropertiesCount();
  }

  loadPropertiesCount() {
    this.isLoading = true;
    this.unitService.getByBrokerId(this.brokerId).subscribe({
      next: (response: any) => {
        this.properties = response.data || [];
        this.isLoading = false;
        this.updateCardVisibility();
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading properties:', error);
        this.properties = [];
        this.isLoading = false;
        this.updateCardVisibility();
        this.cd.detectChanges();
      },
    });
  }

  onSearchTextChange(value: string): void {
    clearTimeout(this.searchTimeout); // Clear previous timeout

    this.searchTimeout = setTimeout(() => {
      this.appliedFilters = {
        ...this.appliedFilters,
        unitType: value.trim(),
      };
      this.cd.detectChanges();
    }, 300);
  }

  toggleFilterDropdown() {
    this.isFilterDropdownVisible = !this.isFilterDropdownVisible;
  }

  onFiltersApplied(filters: any) {
    console.log('Received filters:', filters);
    this.toggleFilterDropdown();
    this.appliedFilters = filters;
    this.cd.detectChanges();
  }

  checkRouteParams() {
    this.route.queryParams.subscribe((params) => {
      if (params['success'] === 'add') {
        this.showSuccessCard = true;
        this.hideCardsAfterDelay();
      } else if (params['success'] === 'publish') {
        this.showPublishCard = true;
        this.hideCardsAfterDelay();
      }
    });
  }

  updateCardVisibility() {
    this.showEmptyCard =
      this.properties.length === 0 &&
      !this.showSuccessCard &&
      !this.showPublishCard;
  }

  hideCardsAfterDelay() {
    setTimeout(() => {
      this.showSuccessCard = false;
      this.showPublishCard = false;
      this.updateCardVisibility();
    }, 5000);
  }

  onBackToTable() {
    this.showSuccessCard = false;
    this.showPublishCard = false;
    this.updateCardVisibility();
    this.cd.detectChanges();
  }

  uploadUnits() {
    console.log('Upload Units clicked');
  }

  downloadTemplate() {
    this.unitService.downloadExcelTemplate().subscribe({
      next: (response: Blob) => {
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'units-template.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error downloading template:', error);
      },
    });
  }
}
